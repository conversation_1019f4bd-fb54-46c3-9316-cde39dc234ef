#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re

# 测试RTF十六进制字符解码
def test_rtf_decode():
    # 从CSV中提取的一个示例十六进制字符串
    hex_string = "7B5C727466315C616E73695C616E73696370673933365C64656666305C6465666C616E67313033335C6465666C616E676665323035327B5C666F6E7474626C7B5C66305C666E696C5C6663686172736574313334205C2763625C2763655C2763635C2765353B7D7B5C66315C666E696C5C666368617273657430204D6963726F736F66742053616E732053657269663B7D7D0A5C766965776B696E64345C7563315C706172645C6B65726E696E67325C66305C66733234202D202D5C2762325C2762665C2763655C2762625C2764345C2765635C2764335C2762305C2763365C2761632B5C2762395C2761365C2763345C2764635C2763365C2761635C2761335C2762612D202D5C2762325C2762665C2763655C2762625C2764365C2766375C2762355C2762635C2762395C2764635C2762345C2764365C2763665C2762385C2762655C2766395C2764345C2763385C2761335C2761635C2764305C2763655C2763635C2761635C2762625C2766395C2762315C2762655C2764355C2766645C2762335C2761335C2761335C2761635C2762375C2764365C2764365C2761375C2762355C2762635C2762395C2764635C2763665C2764395C2763355C2764645C2762335C2765345C2764335C2761665C2763665C2764345C2763665C2766315C2762615C2763335C2761335C2761635C2763655C2762345C2762635C2766625C2763345C2761395C2763395C2764325C2762355C2762635C2762395C2764635C2763305C2761395C2764355C2763355C2762315C2765645C2763665C2764365C2761315C2761335C2763355C2763355C2762665C2764355C2762395C2761365C2763345C2764635C2762615C2763335C2761315C2761335C6C616E67323035325C6B65726E696E67305C66315C7061720A7D"
    
    # 转换为RTF内容
    rtf_content = bytes.fromhex(hex_string).decode('latin-1')
    print("原始RTF内容（前300字符）:")
    print(rtf_content[:300])
    print("\n" + "="*50 + "\n")
    
    # 提取中文字符
    def decode_hex_char(match):
        hex_str = match.group(1)
        try:
            return bytes.fromhex(hex_str).decode('gb2312', errors='ignore')
        except:
            return ''
    
    # 先提取所有的\'xx格式的字符
    hex_chars = re.findall(r"\\'([0-9a-fA-F]{2})", rtf_content)
    print("找到的十六进制字符:")
    for i, hex_char in enumerate(hex_chars[:20]):  # 只显示前20个
        try:
            decoded = bytes.fromhex(hex_char).decode('gb2312', errors='ignore')
            print(f"\\'{hex_char} -> {decoded}")
        except:
            print(f"\\'{hex_char} -> [解码失败]")
    
    print("\n" + "="*50 + "\n")
    
    # 完整转换
    text = rtf_content
    
    # 移除RTF头部信息
    text = re.sub(r'\\rtf\d+.*?\\viewkind\d+', '', text, flags=re.DOTALL)
    
    # 处理常见的RTF控制字符
    replacements = [
        (r'\\par\b', '\n'),  # 段落换行
        (r'\\line\b', '\n'), # 行换行
        (r'\\tab\b', '\t'),  # 制表符
    ]
    
    for pattern, replacement in replacements:
        text = re.sub(pattern, replacement, text)
    
    # 解码中文字符
    text = re.sub(r"\\'([0-9a-fA-F]{2})", decode_hex_char, text)
    
    # 移除其他RTF控制词
    text = re.sub(r'\\[a-zA-Z]+\d*\s?', '', text)
    text = re.sub(r'\\[^a-zA-Z\s]', '', text)
    text = re.sub(r'[{}]', '', text)
    
    # 清理空白字符
    text = re.sub(r'\n\s*\n', '\n', text)
    text = re.sub(r'[ \t]+', ' ', text)
    text = text.strip()
    
    print("最终转换结果:")
    print(text)

if __name__ == "__main__":
    test_rtf_decode()
