#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
报告模板处理脚本
处理CSV文件中WYS和WYG字段的十六进制RTF内容，转换为纯文本格式
"""

import pandas as pd
import re
import logging
from typing import Optional

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('process_log.txt', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


def hex_to_rtf(hex_string: str) -> Optional[str]:
    """
    将十六进制字符串转换为RTF内容
    
    Args:
        hex_string: 十六进制字符串（可能包含0x前缀）
        
    Returns:
        RTF内容字符串，如果转换失败返回None
    """
    if not hex_string or hex_string == "0x" or pd.isna(hex_string):
        return None
    
    try:
        # 移除0x前缀
        if hex_string.startswith("0x"):
            hex_string = hex_string[2:]
        
        # 如果字符串为空，返回None
        if not hex_string:
            return None
            
        # 转换十六进制到bytes，再解码为RTF内容
        rtf_content = bytes.fromhex(hex_string).decode('latin-1')
        return rtf_content
    
    except (ValueError, UnicodeDecodeError) as e:
        logger.warning(f"十六进制转换失败: {str(e)[:100]}...")
        return None


def extract_text_from_rtf(rtf_content: str) -> str:
    """
    从RTF内容中提取纯文本，保留换行符
    
    Args:
        rtf_content: RTF格式的内容
        
    Returns:
        提取的纯文本内容
    """
    if not rtf_content:
        return ""
    
    try:
        # 移除RTF控制字符和格式标记
        text = rtf_content
        
        # 移除RTF头部信息
        text = re.sub(r'\\rtf\d+.*?\\viewkind\d+', '', text, flags=re.DOTALL)
        
        # 处理常见的RTF控制字符
        replacements = [
            (r'\\par\b', '\n'),  # 段落换行
            (r'\\line\b', '\n'), # 行换行
            (r'\\tab\b', '\t'),  # 制表符
            (r'\\[a-zA-Z]+\d*\s?', ''),  # 移除其他RTF控制词
            (r'\\[^a-zA-Z\s]', ''),  # 移除RTF转义字符
            (r'\{', ''),  # 移除左大括号
            (r'\}', ''),  # 移除右大括号
        ]
        
        for pattern, replacement in replacements:
            text = re.sub(pattern, replacement, text)
        
        # 处理十六进制编码的中文字符（如\\'cb\\'ce等）
        def decode_hex_char(match):
            hex_str = match.group(1)
            try:
                return bytes.fromhex(hex_str).decode('gb2312', errors='ignore')
            except:
                return ''
        
        text = re.sub(r"\\'([0-9a-fA-F]{2})", decode_hex_char, text)
        
        # 清理多余的空白字符
        text = re.sub(r'\n\s*\n', '\n', text)  # 移除多余空行
        text = re.sub(r'[ \t]+', ' ', text)    # 合并多个空格
        text = text.strip()
        
        return text
    
    except Exception as e:
        logger.warning(f"RTF文本提取失败: {str(e)}")
        return rtf_content  # 如果提取失败，返回原内容


def process_hex_field(hex_value) -> str:
    """
    处理单个十六进制字段，转换为纯文本
    
    Args:
        hex_value: 十六进制字段值
        
    Returns:
        转换后的纯文本内容
    """
    if pd.isna(hex_value) or hex_value == "0x" or not hex_value:
        return ""
    
    # 转换十六进制为RTF
    rtf_content = hex_to_rtf(hex_value)
    if rtf_content is None:
        return ""
    
    # 从RTF提取纯文本
    text_content = extract_text_from_rtf(rtf_content)
    return text_content


def process_csv_file(input_file: str, output_file: str):
    """
    处理CSV文件，转换WYS和WYG字段
    
    Args:
        input_file: 输入CSV文件路径
        output_file: 输出CSV文件路径
    """
    try:
        logger.info(f"开始处理文件: {input_file}")
        
        # 读取CSV文件，尝试不同编码
        try:
            df = pd.read_csv(input_file, encoding='utf-8')
        except UnicodeDecodeError:
            try:
                df = pd.read_csv(input_file, encoding='gb2312')
            except UnicodeDecodeError:
                df = pd.read_csv(input_file, encoding='latin-1')
        logger.info(f"成功读取CSV文件，共 {len(df)} 行数据")
        
        # 检查必要的列是否存在
        required_columns = ['WYS', 'WYG']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            raise ValueError(f"缺少必要的列: {missing_columns}")
        
        # 处理WYS字段
        logger.info("开始处理WYS字段...")
        df['WYS_processed'] = df['WYS'].apply(process_hex_field)
        processed_wys = df['WYS_processed'].apply(lambda x: len(x) > 0).sum()
        logger.info(f"WYS字段处理完成，成功转换 {processed_wys} 个字段")
        
        # 处理WYG字段
        logger.info("开始处理WYG字段...")
        df['WYG_processed'] = df['WYG'].apply(process_hex_field)
        processed_wyg = df['WYG_processed'].apply(lambda x: len(x) > 0).sum()
        logger.info(f"WYG字段处理完成，成功转换 {processed_wyg} 个字段")
        
        # 更新原字段
        df['WYS'] = df['WYS_processed']
        df['WYG'] = df['WYG_processed']
        
        # 删除临时列
        df = df.drop(['WYS_processed', 'WYG_processed'], axis=1)
        
        # 保存处理后的文件
        df.to_csv(output_file, index=False, encoding='utf-8')
        logger.info(f"处理完成，结果已保存到: {output_file}")
        
        # 输出处理统计
        logger.info(f"处理统计:")
        logger.info(f"- 总行数: {len(df)}")
        logger.info(f"- WYS字段成功转换: {processed_wys} 个")
        logger.info(f"- WYG字段成功转换: {processed_wyg} 个")
        
    except Exception as e:
        logger.error(f"处理文件时发生错误: {str(e)}")
        raise


def main():
    """主函数"""
    input_file = "报告模板.csv"
    output_file = "报告模板_processed.csv"
    
    try:
        process_csv_file(input_file, output_file)
        print(f"\n✅ 处理完成！")
        print(f"输入文件: {input_file}")
        print(f"输出文件: {output_file}")
        print(f"详细日志: process_log.txt")
        
    except Exception as e:
        print(f"\n❌ 处理失败: {str(e)}")
        print(f"请查看日志文件 process_log.txt 获取详细错误信息")


if __name__ == "__main__":
    main()
