#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
报告模板处理脚本
处理报告模板.csv文件中的WYS和WYG字段，将十六进制编码转换为纯文本内容
保留原换行格式
"""

import pandas as pd
import re


def hex_to_rtf_text(hex_string):
    """
    将十六进制字符串转换为RTF内容，然后提取纯文本
    
    Args:
        hex_string: 十六进制字符串（可能包含0x前缀）
        
    Returns:
        提取的纯文本内容
    """
    if not hex_string or hex_string == "0x" or pd.isna(hex_string):
        return ""
    
    try:
        # 移除0x前缀
        if hex_string.startswith("0x"):
            hex_string = hex_string[2:]
        
        # 如果字符串为空，返回空字符串
        if not hex_string:
            return ""
            
        # 转换十六进制到RTF内容
        rtf_content = bytes.fromhex(hex_string).decode('latin-1')
        
        # 从RTF内容中提取纯文本
        text = extract_text_from_rtf(rtf_content)
        return text
        
    except Exception as e:
        print(f"转换失败: {str(e)[:100]}...")
        return ""


def extract_text_from_rtf(rtf_content):
    """
    从RTF内容中提取纯文本，保留换行符
    
    Args:
        rtf_content: RTF格式的内容
        
    Returns:
        提取的纯文本内容
    """
    if not rtf_content:
        return ""
    
    text = rtf_content
    
    # 移除RTF头部信息
    text = re.sub(r'\\rtf\d+.*?\\viewkind\d+', '', text, flags=re.DOTALL)
    
    # 处理RTF控制字符
    # 段落换行
    text = re.sub(r'\\par\b', '\n', text)
    # 行换行
    text = re.sub(r'\\line\b', '\n', text)
    # 制表符
    text = re.sub(r'\\tab\b', '\t', text)
    
    # 处理十六进制编码的中文字符（如\'cb\'ce等）
    def decode_hex_char(match):
        hex_str = match.group(1)
        try:
            return bytes.fromhex(hex_str).decode('gb2312', errors='ignore')
        except:
            return ''
    
    text = re.sub(r"\\'([0-9a-fA-F]{2})", decode_hex_char, text)
    
    # 移除其他RTF控制词和格式标记
    text = re.sub(r'\\[a-zA-Z]+\d*\s?', '', text)  # 移除RTF控制词
    text = re.sub(r'\\[^a-zA-Z\s]', '', text)      # 移除RTF转义字符
    text = re.sub(r'[{}]', '', text)               # 移除大括号
    
    # 清理多余的空白字符，但保留换行
    text = re.sub(r'\n\s*\n', '\n', text)  # 移除多余空行
    text = re.sub(r'[ \t]+', ' ', text)    # 合并多个空格和制表符
    text = text.strip()
    
    return text


def process_csv_file():
    """
    处理报告模板.csv文件，转换WYS和WYG字段
    """
    input_file = "报告模板.csv"
    output_file = "报告模板_converted.csv"
    
    print(f"开始处理文件: {input_file}")
    
    # 读取CSV文件
    try:
        df = pd.read_csv(input_file, encoding='utf-8')
    except UnicodeDecodeError:
        try:
            df = pd.read_csv(input_file, encoding='gb2312')
        except UnicodeDecodeError:
            df = pd.read_csv(input_file, encoding='latin-1')
    
    print(f"成功读取CSV文件，共 {len(df)} 行数据")
    
    # 检查WYS和WYG字段是否存在
    if 'WYS' not in df.columns or 'WYG' not in df.columns:
        print("错误：CSV文件中缺少WYS或WYG字段")
        return
    
    # 处理WYS字段
    print("正在处理WYS字段...")
    df['WYS'] = df['WYS'].apply(hex_to_rtf_text)
    wys_converted = df['WYS'].apply(lambda x: len(x) > 0).sum()
    print(f"WYS字段处理完成，成功转换 {wys_converted} 个字段")
    
    # 处理WYG字段
    print("正在处理WYG字段...")
    df['WYG'] = df['WYG'].apply(hex_to_rtf_text)
    wyg_converted = df['WYG'].apply(lambda x: len(x) > 0).sum()
    print(f"WYG字段处理完成，成功转换 {wyg_converted} 个字段")
    
    # 保存处理后的文件
    df.to_csv(output_file, index=False, encoding='utf-8')
    print(f"处理完成，结果已保存到: {output_file}")
    
    # 输出处理统计
    print(f"\n处理统计:")
    print(f"- 总行数: {len(df)}")
    print(f"- WYS字段成功转换: {wys_converted} 个")
    print(f"- WYG字段成功转换: {wyg_converted} 个")


def main():
    """主函数"""
    try:
        process_csv_file()
        print("\n✅ 处理完成！")
        
    except Exception as e:
        print(f"\n❌ 处理失败: {str(e)}")


if __name__ == "__main__":
    main()
